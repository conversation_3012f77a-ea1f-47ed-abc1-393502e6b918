@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules\eslint-config-prettier\bin\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules\eslint-config-prettier\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\eslint-config-prettier@10.1.8_eslint@9.33.0_jiti@2.5.1_\node_modules;C:\Users\<USER>\3D Objects\ggg\foodime\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\eslint-config-prettier\bin\cli.js" %*
)
