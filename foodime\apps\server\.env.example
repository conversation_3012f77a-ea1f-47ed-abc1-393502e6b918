# Server Configuration
PORT=3001
NODE_ENV=development

# Database
DATABASE_URL="postgresql://foodime_user:foodime_password@localhost:5432/foodime_db"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-in-production"
JWT_EXPIRES_IN="7d"

# Redis (Optional)
REDIS_URL="redis://localhost:6379"

# File Upload
UPLOAD_PATH="./uploads"
MAX_FILE_SIZE=5242880
CORS_ORIGINS="http://localhost:3000,http://localhost:3001,http://localhost:19006"

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Email Configuration
EMAIL_HOST="smtp.gmail.com"
EMAIL_PORT=587
EMAIL_USER="<EMAIL>"
EMAIL_PASS="your-app-password"

# Payment Integration (Stripe) - Optional
STRIPE_PUBLISHABLE_KEY=""
STRIPE_SECRET_KEY=""
STRIPE_WEBHOOK_SECRET=""

# AI Providers (for RFLCT AI integration) - Optional
OPENAI_API_KEY=""
ANTHROPIC_API_KEY=""
GOOGLE_AI_API_KEY=""
ELEVENLABS_API_KEY=""
JEST_SMOKE_TEST=""

# Auth0 Configuration
AUTH0_DOMAIN=your-auth0-domain
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret