name: Server CI/CD

on:
  push:
    branches: [main, dev]
    paths:
      - "apps/server/**"
      - "packages/**"
      - "pnpm-lock.yaml"
  pull_request:
    branches: [main, dev]
    paths:
      - "apps/server/**"
      - "packages/**"
      - "pnpm-lock.yaml"

env:
  NODE_VERSION: "18"
  PNPM_VERSION: "9.0.0"

jobs:
  build-and-test:
    name: 🏗️ Build & Test Server
    runs-on: ubuntu-latest

    steps:
      - name: 🛒 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 📥 Install dependencies
        run: pnpm install --frozen-lockfile

      - name: 🔍 Lint
        run: pnpm --filter=server lint

      - name: 🔍 Type check
        run: pnpm --filter=server check-types

      - name: 🏗️ Build
        run: pnpm --filter=server build

      - name: 🧪 Run tests
        run: pnpm --filter=server test:ci
        env:
          NODE_ENV: test
          CI: true
