// Jest Snapshot v1, https://jestjs.io/docs/snapshot-testing

exports[`App matches snapshot 1`] = `
[
  <NavigationContainer>
    <Navigator
      initialRouteName="Home"
    >
      <ScrollView
        contentInsetAdjustmentBehavior="automatic"
      >
        <View
          style={
            {
              "alignItems": "center",
              "padding": 20,
            }
          }
        >
          <Text
            style={
              {
                "color": "#333",
                "fontSize": 28,
                "fontWeight": "bold",
                "marginBottom": 10,
                "textAlign": "center",
              }
            }
          >
            Welcome to Foodime!
          </Text>
          <Text
            style={
              {
                "color": "#666",
                "fontSize": 18,
                "marginBottom": 20,
                "textAlign": "center",
              }
            }
          >
            Your mobile app is ready
          </Text>
          <Text
            style={
              {
                "color": "#444",
                "fontSize": 16,
                "lineHeight": 24,
                "marginBottom": 30,
                "textAlign": "center",
              }
            }
          >
            This is your React Native app with Expo, integrated into the Foodime monorepo.
          </Text>
          <View
            style={
              {
                "alignSelf": "stretch",
                "backgroundColor": "#f5f5f5",
                "borderRadius": 10,
                "padding": 20,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#333",
                  "fontSize": 18,
                  "fontWeight": "bold",
                  "marginBottom": 15,
                }
              }
            >
              Features included:
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • React Navigation
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • Safe Area handling
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • TypeScript support
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • ESLint configuration
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • Shared UI components
            </Text>
            <Text
              style={
                {
                  "color": "#555",
                  "fontSize": 16,
                  "lineHeight": 22,
                  "marginBottom": 8,
                }
              }
            >
              • Monorepo integration
            </Text>
          </View>
          <TouchableOpacity
            onPress={[Function]}
            style={
              {
                "backgroundColor": "#007AFF",
                "borderRadius": 25,
                "elevation": 5,
                "marginTop": 30,
                "paddingHorizontal": 30,
                "paddingVertical": 15,
                "shadowColor": "#000",
                "shadowOffset": {
                  "height": 2,
                  "width": 0,
                },
                "shadowOpacity": 0.25,
                "shadowRadius": 3.84,
              }
            }
          >
            <Text
              style={
                {
                  "color": "#fff",
                  "fontSize": 18,
                  "fontWeight": "600",
                  "textAlign": "center",
                }
              }
            >
              Test App 🚀
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </Navigator>
  </NavigationContainer>,
  <StatusBar
    style="auto"
  />,
]
`;
