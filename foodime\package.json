{"name": "foodime", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types", "test": "turbo run test", "test:unit": "turbo run test:unit", "test:integration": "turbo run test:integration", "test:e2e": "turbo run test:e2e", "test:coverage": "turbo run test:coverage", "test:ci": "turbo run test:ci", "test:watch": "turbo run test:watch", "prepare": "husky install"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "commitizen": "^4.3.1", "cspell": "^9.1.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.27.0", "husky": "^9.1.7", "prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "pnpm": {"overrides": {"@types/express": "4.17.23"}}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}