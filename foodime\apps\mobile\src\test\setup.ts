// Note: extend-expect is built into React Native Testing Library v12.4+

// Mock react-native-safe-area-context
jest.mock('react-native-safe-area-context', () => ({
  SafeAreaProvider: ({ children }: { children: React.ReactNode }) => children,
  SafeAreaView: ({ children }: { children: React.ReactNode }) => children,
  useSafeAreaInsets: () => ({ top: 0, right: 0, bottom: 0, left: 0 }),
  useSafeAreaFrame: () => ({ x: 0, y: 0, width: 390, height: 844 }),
}));

// Mock @react-navigation/native
jest.mock('@react-navigation/native', () => {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const React = require('react');
  return {
    useNavigation: () => ({
      navigate: jest.fn(),
      goBack: jest.fn(),
      dispatch: jest.fn(),
      setParams: jest.fn(),
      addListener: jest.fn(),
      removeListener: jest.fn(),
    }),
    useRoute: () => ({
      params: {},
      name: '<PERSON><PERSON><PERSON>out<PERSON>',
      key: 'Mock<PERSON>oute<PERSON><PERSON>',
    }),
    useFocusEffect: jest.fn(),
    NavigationContainer: ({ children }: { children: React.ReactNode }) => {
      return React.createElement('NavigationContainer', {}, children);
    },
    DefaultTheme: { colors: { background: '#ffffff' } },
    ThemeProvider: ({ children }: { children: React.ReactNode }) => children,
  };
});

// Mock @react-navigation/native-stack
jest.mock('@react-navigation/native-stack', () => {
  // eslint-disable-next-line @typescript-eslint/no-require-imports
  const React = require('react');
  return {
    createNativeStackNavigator: () => ({
      Navigator: ({ children, initialRouteName }: { children: React.ReactNode; initialRouteName?: string }) => {
        return React.createElement('Navigator', { initialRouteName }, children);
      },
      Screen: ({ component: Component, name }: { component: React.ComponentType<Record<string, unknown>>; name: string }) => {
        return React.createElement(Component, { key: name });
      },
    }),
  };
});

// Mock Expo modules
jest.mock('expo-status-bar', () => ({
  StatusBar: 'StatusBar',
}));

jest.mock('expo-constants', () => ({
  default: {
    statusBarHeight: 20,
    deviceName: 'iPhone',
    platform: { ios: { platform: 'ios' } },
  },
}));

// Mock Alert
jest.mock('react-native/Libraries/Alert/Alert', () => ({
  alert: jest.fn(),
}));

// Note: NativeAnimatedHelper mock removed as it's not accessible in this version

// Global test timeout
jest.setTimeout(10000);

// Suppress deprecation warnings for tests
const originalConsoleError = console.error;
console.error = (...args) => {
  if (
    typeof args[0] === 'string' &&
    args[0].includes('react-test-renderer is deprecated')
  ) {
    return;
  }
  originalConsoleError.apply(console, args);
};
