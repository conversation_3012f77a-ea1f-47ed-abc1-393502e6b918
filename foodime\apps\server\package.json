{"name": "@foodime/server", "version": "1.0.0", "private": true, "main": "src/index.ts", "scripts": {"dev": "set NODE_ENV=development && ts-node-dev -r tsconfig-paths/register src/index.ts --max-old-space-size=4096", "build": "tsc -p . -outDir ./dist", "lint": "eslint .", "test": "jest --coverage", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:smoke": "JEST_SMOKE_TEST=true jest smoke.test.ts", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "generate": "drizzle-kit generate", "migrate": "drizzle-kit push", "seed": "tsx src/db/seeds/index.ts", "seed:dev": "NODE_ENV=development tsx src/db/seeds/index.ts", "seed:test": "NODE_ENV=test tsx src/db/seeds/index.ts"}, "dependencies": {"@apollo/server": "^5.0.0", "@apollo/server-plugin-response-cache": "^5.0.0", "@as-integrations/express4": "^1.1.2", "auth0": "^4.28.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "class-validator": "^0.14.1", "compression": "^1.7.5", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.4.7", "drizzle-orm": "^0.44.4", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.0", "graphql": "^16.8.1", "graphql-query-complexity": "^1.1.0", "helmet": "^8.0.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.2.1", "nodemailer": "^6.9.16", "pg": "^8.13.1", "redis": "^4.7.0", "reflect-metadata": "^0.2.1", "request-ip": "^3.3.0", "sharp": "^0.33.5", "socket.io": "^4.8.1", "stripe": "^17.4.0", "type-graphql": "^2.0.0-beta.6", "uuid": "^11.0.3", "winston": "^3.17.0", "zod": "^3.24.1"}, "devDependencies": {"@jest/transform": "29.7.0", "@jest/types": "29.6.3", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.7", "@types/lodash": "^4.17.13", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.10.1", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.11.10", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "babel-jest": "29.7.0", "drizzle-kit": "^0.31.4", "eslint": "^9.27.0", "jest": "^29.7.0", "nodemon": "^3.1.9", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.7.0", "typescript": "^5.8.3"}}