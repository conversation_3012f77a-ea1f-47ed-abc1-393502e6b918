import { pgTable, uuid, timestamp, text } from 'drizzle-orm/pg-core';
import { users } from './users';
import { tenants, locations } from './tenants';
import { plans } from './plans';

// Subscriptions table
export const subscriptions = pgTable('subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  tenantId: uuid('tenant_id').references(() => tenants.id),
  planId: uuid('plan_id').references(() => plans.id),
  subscriberId: text('subscriber_id').references(() => users.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Subscription locations
export const subscriptionLocations = pgTable('subscription_locations', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => subscriptions.id),
  locationId: uuid('location_id').references(() => locations.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Addon catalog
export const addonsCatalog = pgTable('addons_catalog', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description').default(''),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Subscription addons
export const subscriptionAddons = pgTable('subscription_addons', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  description: text('description').default(''),
  subscriptionId: uuid('subscription_id').references(() => subscriptions.id),
  addonId: uuid('addon_id').references(() => addonsCatalog.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type Subscription = typeof subscriptions.$inferSelect;
export type NewSubscription = typeof subscriptions.$inferInsert;

export type SubscriptionLocation = typeof subscriptionLocations.$inferSelect;
export type NewSubscriptionLocation = typeof subscriptionLocations.$inferInsert;

export type AddonsCatalog = typeof addonsCatalog.$inferSelect;
export type NewAddonsCatalog = typeof addonsCatalog.$inferInsert;

export type SubscriptionAddon = typeof subscriptionAddons.$inferSelect;
export type NewSubscriptionAddon = typeof subscriptionAddons.$inferInsert;
