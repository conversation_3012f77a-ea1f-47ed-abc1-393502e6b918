import { pgTable, uuid, timestamp, text } from 'drizzle-orm/pg-core';
import { users } from './users';
import { plans } from './plans';

// Cost components
export const costComponents = pgTable('cost_components', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Plan costs
export const planCosts = pgTable('plan_costs', {
  id: uuid('id').primaryKey().defaultRandom(),
  planId: uuid('plan_id').references(() => plans.id),
  costComponentId: uuid('cost_component_id').references(() => costComponents.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Revenue snapshots
export const revenueSnapshots = pgTable('revenue_snapshots', {
  id: uuid('id').primaryKey().defaultRandom(),
  planId: uuid('plan_id').references(() => plans.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Margin snapshot
export const marginSnapshots = pgTable('margin_snapshots', {
  id: uuid('id').primaryKey().defaultRandom(),
  planId: uuid('plan_id').references(() => plans.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// SLA tiers
export const slaTiers = pgTable('sla_tiers', {
  id: uuid('id').primaryKey().defaultRandom(),
  planId: uuid('plan_id').references(() => plans.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type CostComponent = typeof costComponents.$inferSelect;
export type NewCostComponent = typeof costComponents.$inferInsert;

export type PlanCost = typeof planCosts.$inferSelect;
export type NewPlanCost = typeof planCosts.$inferInsert;

export type RevenueSnapshot = typeof revenueSnapshots.$inferSelect;
export type NewRevenueSnapshot = typeof revenueSnapshots.$inferInsert;

export type MarginSnapshot = typeof marginSnapshots.$inferSelect;
export type NewMarginSnapshot = typeof marginSnapshots.$inferInsert;

export type SlaTier = typeof slaTiers.$inferSelect;
export type NewSlaTier = typeof slaTiers.$inferInsert;
