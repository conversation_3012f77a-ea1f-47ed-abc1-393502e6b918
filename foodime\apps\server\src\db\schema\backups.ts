import { pgTable, uuid, timestamp, text, boolean, jsonb } from 'drizzle-orm/pg-core';
import { users } from './users';
import { tenants } from './tenants';

// Backups table
export const backups = pgTable('backups', {
  id: uuid('id').primaryKey().defaultRandom(),
  tenantId: uuid('tenant_id').references(() => tenants.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Branding table
export const branding = pgTable('branding', {
  id: uuid('id').primaryKey().defaultRandom(),
  tenantId: uuid('tenant_id').references(() => tenants.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type Backup = typeof backups.$inferSelect;
export type NewBackup = typeof backups.$inferInsert;

export type Branding = typeof branding.$inferSelect;
export type NewBranding = typeof branding.$inferInsert;
