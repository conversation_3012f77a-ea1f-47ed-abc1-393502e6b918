import 'reflect-metadata';
import 'express-async-errors';
import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import rateLimit from 'express-rate-limit';
import { createServer, Server as HttpServer } from 'http';
import { Server as SocketIOServer } from 'socket.io';
import path from 'path';

import config from './config';
import { connectDatabase, disconnectDatabase } from './db';
import { logger, morganStream } from './utils/logger';

// Import routes
import authRoutes from './routes/auth';
import userRoutes from './routes/users';
import restaurantRoutes from './routes/restaurants';
import menuRoutes from './routes/menu';
import orderRoutes from './routes/orders';
import reviewRoutes from './routes/reviews';
import notificationRoutes from './routes/notifications';
import aiRoutes from './routes/ai';
import rflctRoutes from './routes/rflct';
import analyticsRoutes from './routes/analytics';
import uploadRoutes from './routes/upload';
// Error handling middleware
import { errorHandler } from './middleware/errorHandler';
import { notFound } from './middleware/notFound';
import { createGraphQLServer, createGraphQLMiddleware } from './graphql/server';

class Server {
  public app: Application;
  public httpServer: HttpServer;
  public io: SocketIOServer;

  constructor() {
    this.app = express();
    this.httpServer = createServer(this.app);
    this.io = new SocketIOServer(this.httpServer, {
      cors: {
        origin: config.corsOrigins,
        methods: ['GET', 'POST'],
      },
    });

    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeSocketIO();
    // Note: GraphQL and error handling will be initialized in start() method
  }

  private initializeMiddlewares(): void {
    this.app.use(
      helmet({
        contentSecurityPolicy: config.nodeEnv === 'development' ? false : true,
      })
    );

    // CORS
    this.app.use(cors({
        origin: config.corsOrigins,
        credentials: true,
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: config.rateLimitWindow,
      max: config.rateLimitMax,
      message: {
        success: false,
        message: 'Too many requests from this IP, please try again later.',
      },
    });
    this.app.use(limiter);

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true }));

    // Compression
    this.app.use(compression());

    // Logging
    this.app.use(morgan('combined', { stream: morganStream }));

    // Static files
    this.app.use('/uploads', express.static(path.join(__dirname, '../uploads')));
    this.app.use('/public', express.static(path.join(__dirname, '../public')));

    // Health check
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        success: true,
        message: 'Server is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    });
  }

  private initializeRoutes(): void {
    const apiPrefix = '/api/v1';

    // API Routes
    this.app.use(`${apiPrefix}/auth`, authRoutes);
    this.app.use(`${apiPrefix}/users`, userRoutes);
    this.app.use(`${apiPrefix}/restaurants`, restaurantRoutes);
    this.app.use(`${apiPrefix}/menu`, menuRoutes);
    this.app.use(`${apiPrefix}/orders`, orderRoutes);
    this.app.use(`${apiPrefix}/reviews`, reviewRoutes);
    this.app.use(`${apiPrefix}/notifications`, notificationRoutes);
    this.app.use(`${apiPrefix}/ai`, aiRoutes);
    this.app.use(`${apiPrefix}/rflct`, rflctRoutes);
    this.app.use(`${apiPrefix}/analytics`, analyticsRoutes);
    this.app.use(`${apiPrefix}/upload`, uploadRoutes);

    // Root endpoint
    this.app.get('/', (req: Request, res: Response) => {
      res.json({
        success: true,
        message: 'Foodime Server API',
        version: '1.0.0',
        endpoints: {
          health: '/health',
          api: '/api/v1',
          documentation: '/api/v1/docs',
        },
      });
    });
  }

  private initializeSocketIO(): void {
    this.io.on('connection', (socket) => {
      logger.info(`Socket connected: ${socket.id}`);

      // Join user-specific room
      socket.on('join-user', (userId: string) => {
        socket.join(`user-${userId}`);
        logger.debug(`User ${userId} joined their room`);
      });

      // Join order-specific room
      socket.on('join-order', (orderId: string) => {
        socket.join(`order-${orderId}`);
        logger.debug(`Socket joined order room: ${orderId}`);
      });

      // Handle AI chat events
      socket.on('ai-message', (data) => {
        // Broadcast to specific chat room
        socket.to(`chat-${data.chatId}`).emit('ai-message', data);
      });

      // Handle disconnection
      socket.on('disconnect', () => {
        logger.info(`Socket disconnected: ${socket.id}`);
      });
    });

    // Make io accessible to routes
    this.app.set('io', this.io);
  }

  private async initializeGraphQL(): Promise<void> {
    const server = await createGraphQLServer(this.httpServer);
    const gqlMiddleware = createGraphQLMiddleware(server);
    this.app.use('/graphql', gqlMiddleware);
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFound);

    // Global error handler
    this.app.use(errorHandler);
  }

  public async start(): Promise<void> {
    try {
      // Connect to database
      await connectDatabase();

      // Initialize GraphQL server
      await this.initializeGraphQL();

      // Initialize error handling (must be after GraphQL initialization)
      this.initializeErrorHandling();

      // Start server
      this.httpServer.listen(config.port, () => {
        logger.info(`Server running on port ${config.port}`);
        logger.info(`Environment: ${config.nodeEnv}`);
        logger.info(`Health check: http://localhost:${config.port}/health`);
        logger.info(
          `API Documentation: http://localhost:${config.port}/api/v1`
        );
        logger.info(
          `GraphQL Playground: http://localhost:${config.port}/graphql`
        );
      });

      // Graceful shutdown handlers
      process.on('SIGTERM', this.shutdown.bind(this));
      process.on('SIGINT', this.shutdown.bind(this));
      process.on('uncaughtException', (error) => {
        logger.error('Uncaught Exception:', error);
        this.shutdown();
      });
      process.on('unhandledRejection', (reason, promise) => {
        logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
        this.shutdown();
      });
    } catch (error) {
      logger.error('Failed to start server:', error);
      process.exit(1);
    }
  }

  private async shutdown(): Promise<void> {
    logger.info('Shutting down server...');

    try {
      // Close HTTP server
      await new Promise<void>((resolve) => {
        this.httpServer.close(() => {
          logger.info('HTTP server closed');
          resolve();
        });
      });

      // Close Socket.IO
      this.io.close(() => {
        logger.info('Socket.IO server closed');
      });

      // Disconnect from database
      await disconnectDatabase();

      logger.info('Server shutdown complete');
      process.exit(0);
    } catch (error) {
      logger.error('Error during shutdown:', error);
      process.exit(1);
    }
  }
}

// Create and start server
const server = new Server();
server.start();

// Export for testing
export default server;
