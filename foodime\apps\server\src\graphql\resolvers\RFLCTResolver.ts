import { Resolver, Query, Mutation, Arg, Ctx, UseMiddleware, ID, InputType, Field } from 'type-graphql';
import { Length, IsOptional } from 'class-validator';
import { RFLCTCode, RFLCTType } from '../entities/RFLCTCode';
import { User } from '../entities/User';
import { db } from '../../config/database';
import { logger } from '../../utils/logger';

// Input types
@InputType()
class CreateRFLCTCodeInput {
  @Field()
  @Length(4, 4) // Enforces 4-digit codes as per user preference
  code!: string;

  @Field(() => RFLCTType)
  type!: RFLCTType;

  @Field({ nullable: true })
  @IsOptional()
  description?: string;

  @Field({ nullable: true })
  @IsOptional()
  userId?: string;
}

// Context type
interface Context {
  user?: User;
}

// Authentication middleware
function AuthMiddleware({ context }: { context: Context }, next: () => Promise<any>) {
  if (!context.user) {
    throw new Error('Authentication required');
  }
  return next();
}

@Resolver(RFLCTCode)
export class RFLCTResolver {

  @Query(() => [RFLCTCode])
  @UseMiddleware(AuthMiddleware)
  async rflctCodes(
    @Arg('limit', { defaultValue: 50 }) limit: number,
    @Arg('offset', { defaultValue: 0 }) offset: number,
    @Arg('isActive', { nullable: true }) isActive?: boolean
  ): Promise<RFLCTCode[]> {
    let query = `
      SELECT id, code, type, description, is_active as "isActive", user_id as "userId",
             usage_count as "usageCount", last_used as "lastUsed", metadata,
             created_at as "createdAt", updated_at as "updatedAt"
      FROM rflct_codes
    `;
    
    const values: any[] = [];
    let paramIndex = 1;

    if (isActive !== undefined) {
      query += ` WHERE is_active = $${paramIndex++}`;
      values.push(isActive);
    }

    query += ` ORDER BY created_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex}`;
    values.push(limit, offset);

    const result = await db.query(query, values);
    return result.rows;
  }

  @Query(() => RFLCTCode, { nullable: true })
  async rflctCode(@Arg('code') code: string): Promise<RFLCTCode | null> {
    const query = `
      SELECT id, code, type, description, is_active as "isActive", user_id as "userId",
             usage_count as "usageCount", last_used as "lastUsed", metadata,
             created_at as "createdAt", updated_at as "updatedAt"
      FROM rflct_codes
      WHERE code = $1
    `;
    
    const result = await db.query(query, [code]);
    return result.rows[0] || null;
  }

  @Mutation(() => RFLCTCode)
  @UseMiddleware(AuthMiddleware)
  async createRFLCTCode(@Arg('input') input: CreateRFLCTCodeInput): Promise<RFLCTCode> {
    try {
      // Validate 4-digit code format
      if (!/^\d{4}$/.test(input.code)) {
        throw new Error('RFLCT code must be exactly 4 digits');
      }

      // Check if code already exists
      const existingCode = await this.rflctCode(input.code);
      if (existingCode) {
        throw new Error('RFLCT code already exists');
      }

      const query = `
        INSERT INTO rflct_codes (code, type, description, user_id, is_active, usage_count)
        VALUES ($1, $2, $3, $4, true, 0)
        RETURNING id, code, type, description, is_active as "isActive", user_id as "userId",
                  usage_count as "usageCount", last_used as "lastUsed", metadata,
                  created_at as "createdAt", updated_at as "updatedAt"
      `;

      const values = [
        input.code,
        input.type,
        input.description || null,
        input.userId || null,
      ];

      const result = await db.query(query, values);
      const newCode = result.rows[0];

      logger.info(`RFLCT code created: ${input.code} (type: ${input.type})`);

      return newCode;
    } catch (error) {
      logger.error('Create RFLCT code error:', error);
      throw new Error(`Failed to create RFLCT code: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  @Mutation(() => RFLCTCode)
  async useRFLCTCode(@Arg('code') code: string, @Ctx() ctx: Context): Promise<RFLCTCode> {
    try {
      // Find the code
      const rflctCode = await this.rflctCode(code);
      if (!rflctCode) {
        throw new Error('RFLCT code not found');
      }

      if (!rflctCode.isActive) {
        throw new Error('RFLCT code is inactive');
      }

      // Update usage count and last used
      const query = `
        UPDATE rflct_codes
        SET usage_count = usage_count + 1, last_used = NOW(), updated_at = NOW()
        WHERE code = $1
        RETURNING id, code, type, description, is_active as "isActive", user_id as "userId",
                  usage_count as "usageCount", last_used as "lastUsed", metadata,
                  created_at as "createdAt", updated_at as "updatedAt"
      `;

      const result = await db.query(query, [code]);
      const updatedCode = result.rows[0];

      logger.info(`RFLCT code used: ${code} (usage count: ${updatedCode.usageCount})`);

      // Here you could implement specific logic based on the code type
      switch (updatedCode.type) {
        case RFLCTType.USER_ACCESS:
          logger.info(`User access granted via RFLCT code: ${code}`);
          break;
        case RFLCTType.FEATURE_UNLOCK:
          logger.info(`Feature unlocked via RFLCT code: ${code}`);
          break;
        case RFLCTType.PROMOTION:
          logger.info(`Promotion applied via RFLCT code: ${code}`);
          break;
        // Add more cases as needed
      }

      return updatedCode;
    } catch (error) {
      logger.error('Use RFLCT code error:', error);
      throw new Error(`Failed to use RFLCT code: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  @Mutation(() => Boolean)
  @UseMiddleware(AuthMiddleware)
  async deactivateRFLCTCode(@Arg('code') code: string): Promise<boolean> {
    try {
      const query = `
        UPDATE rflct_codes
        SET is_active = false, updated_at = NOW()
        WHERE code = $1
      `;

      const result = await db.query(query, [code]);
      
      if (result.rowCount === 0) {
        throw new Error('RFLCT code not found');
      }

      logger.info(`RFLCT code deactivated: ${code}`);
      return true;
    } catch (error) {
      logger.error('Deactivate RFLCT code error:', error);
      throw new Error(`Failed to deactivate RFLCT code: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  @Query(() => [RFLCTCode])
  @UseMiddleware(AuthMiddleware)
  async generateRandomRFLCTCodes(
    @Arg('count', { defaultValue: 10 }) count: number,
    @Arg('type') type: RFLCTType
  ): Promise<string[]> {
    const codes: string[] = [];
    
    for (let i = 0; i < Math.min(count, 100); i++) {
      let code: string;
      let attempts = 0;
      
      // Generate unique 4-digit code
      do {
        code = Math.floor(1000 + Math.random() * 9000).toString();
        attempts++;
        
        // Prevent infinite loop
        if (attempts > 100) {
          throw new Error('Unable to generate unique RFLCT codes');
        }
      } while (await this.rflctCode(code));
      
      codes.push(code);
    }

    logger.info(`Generated ${codes.length} unique 4-digit RFLCT codes`);
    return codes;
  }
}
