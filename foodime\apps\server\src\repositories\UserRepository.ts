import { User, UserRole } from '../graphql/entities/User';
import { db } from '../config/database';
import { logger } from '../utils/logger';

interface CreateUserInput {
  email: string;
  password: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  role?: UserRole;
}

interface UpdateUserInput {
  email?: string;
  username?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  dateOfBirth?: Date;
  avatar?: string;
  isVerified?: boolean;
  isActive?: boolean;
  role?: UserRole;
  lastLogin?: Date;
}

export class UserRepository {
  async findAll(limit = 50, offset = 0): Promise<User[]> {
    try {
      const query = `
        SELECT id, email, username, first_name as "firstName", last_name as "lastName",
               avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
               is_active as "isActive", role, last_login as "lastLogin",
               created_at as "createdAt", updated_at as "updatedAt"
        FROM users 
        ORDER BY created_at DESC 
        LIMIT $1 OFFSET $2
      `;
      const result = await db.query(query, [limit, offset]);
      return result.rows;
    } catch (error) {
      logger.error('Error finding all users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  async findById(id: string): Promise<User | null> {
    try {
      const query = `
        SELECT id, email, username, first_name as "firstName", last_name as "lastName",
               avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
               is_active as "isActive", role, last_login as "lastLogin",
               created_at as "createdAt", updated_at as "updatedAt"
        FROM users 
        WHERE id = $1
      `;
      const result = await db.query(query, [id]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by ID:', error);
      throw new Error('Failed to fetch user');
    }
  }

  async findByEmail(email: string): Promise<(User & { password: string }) | null> {
    try {
      const query = `
        SELECT id, email, username, password, first_name as "firstName", last_name as "lastName",
               avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
               is_active as "isActive", role, last_login as "lastLogin",
               created_at as "createdAt", updated_at as "updatedAt"
        FROM users 
        WHERE email = $1
      `;
      const result = await db.query(query, [email]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by email:', error);
      throw new Error('Failed to fetch user');
    }
  }

  async findByUsername(username: string): Promise<User | null> {
    try {
      const query = `
        SELECT id, email, username, first_name as "firstName", last_name as "lastName",
               avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
               is_active as "isActive", role, last_login as "lastLogin",
               created_at as "createdAt", updated_at as "updatedAt"
        FROM users 
        WHERE username = $1
      `;
      const result = await db.query(query, [username]);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error finding user by username:', error);
      throw new Error('Failed to fetch user');
    }
  }

  async create(input: CreateUserInput): Promise<User> {
    try {
      const query = `
        INSERT INTO users (email, password, username, first_name, last_name, phone, date_of_birth, role)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING id, email, username, first_name as "firstName", last_name as "lastName",
                  avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
                  is_active as "isActive", role, last_login as "lastLogin",
                  created_at as "createdAt", updated_at as "updatedAt"
      `;
      const result = await db.query(query, [
        input.email,
        input.password,
        input.username || null,
        input.firstName || null,
        input.lastName || null,
        input.phone || null,
        input.dateOfBirth || null,
        input.role || UserRole.USER,
      ]);
      return result.rows[0];
    } catch (error) {
      logger.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  async update(id: string, input: UpdateUserInput): Promise<User | null> {
    try {
      const setParts: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (input.email !== undefined) {
        setParts.push(`email = $${paramIndex++}`);
        values.push(input.email);
      }
      if (input.username !== undefined) {
        setParts.push(`username = $${paramIndex++}`);
        values.push(input.username);
      }
      if (input.firstName !== undefined) {
        setParts.push(`first_name = $${paramIndex++}`);
        values.push(input.firstName);
      }
      if (input.lastName !== undefined) {
        setParts.push(`last_name = $${paramIndex++}`);
        values.push(input.lastName);
      }
      if (input.phone !== undefined) {
        setParts.push(`phone = $${paramIndex++}`);
        values.push(input.phone);
      }
      if (input.dateOfBirth !== undefined) {
        setParts.push(`date_of_birth = $${paramIndex++}`);
        values.push(input.dateOfBirth);
      }
      if (input.avatar !== undefined) {
        setParts.push(`avatar = $${paramIndex++}`);
        values.push(input.avatar);
      }
      if (input.isVerified !== undefined) {
        setParts.push(`is_verified = $${paramIndex++}`);
        values.push(input.isVerified);
      }
      if (input.isActive !== undefined) {
        setParts.push(`is_active = $${paramIndex++}`);
        values.push(input.isActive);
      }
      if (input.role !== undefined) {
        setParts.push(`role = $${paramIndex++}`);
        values.push(input.role);
      }
      if (input.lastLogin !== undefined) {
        setParts.push(`last_login = $${paramIndex++}`);
        values.push(input.lastLogin);
      }

      if (setParts.length === 0) {
        return this.findById(id);
      }

      setParts.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(id);

      const query = `
        UPDATE users 
        SET ${setParts.join(', ')}
        WHERE id = $${paramIndex}
        RETURNING id, email, username, first_name as "firstName", last_name as "lastName",
                  avatar, phone, date_of_birth as "dateOfBirth", is_verified as "isVerified",
                  is_active as "isActive", role, last_login as "lastLogin",
                  created_at as "createdAt", updated_at as "updatedAt"
      `;
      const result = await db.query(query, values);
      return result.rows[0] || null;
    } catch (error) {
      logger.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  async updatePassword(id: string, hashedPassword: string): Promise<boolean> {
    try {
      const query = `
        UPDATE users 
        SET password = $1, updated_at = CURRENT_TIMESTAMP
        WHERE id = $2
      `;
      const result = await db.query(query, [hashedPassword, id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error updating password:', error);
      throw new Error('Failed to update password');
    }
  }

  async delete(id: string): Promise<boolean> {
    try {
      const query = `DELETE FROM users WHERE id = $1`;
      const result = await db.query(query, [id]);
      return (result.rowCount ?? 0) > 0;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  }

  async count(): Promise<number> {
    try {
      const query = `SELECT COUNT(*) as count FROM users`;
      const result = await db.query(query);
      return parseInt(result.rows[0].count, 10);
    } catch (error) {
      logger.error('Error counting users:', error);
      throw new Error('Failed to count users');
    }
  }
}
