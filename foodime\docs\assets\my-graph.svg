<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 2.43.0 (0)
 -->
<!-- Title: %3 Pages: 1 -->
<svg width="1331pt" height="332pt"
 viewBox="0.00 0.00 1330.92 332.00" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(4 328)">
<title>%3</title>
<polygon fill="white" stroke="transparent" points="-4,4 -4,-328 1326.92,-328 1326.92,4 -4,4"/>
<!-- [root] ___ROOT___#build -->
<g id="node1" class="node">
<title>[root] ___ROOT___#build</title>
<ellipse fill="none" stroke="black" cx="477.19" cy="-90" rx="105.88" ry="18"/>
<text text-anchor="middle" x="477.19" y="-86.3" font-family="Times,serif" font-size="14.00">[root] ___ROOT___#build</text>
</g>
<!-- [root] ___ROOT___ -->
<g id="node2" class="node">
<title>[root] ___ROOT___</title>
<ellipse fill="none" stroke="black" cx="1017.19" cy="-18" rx="83.39" ry="18"/>
<text text-anchor="middle" x="1017.19" y="-14.3" font-family="Times,serif" font-size="14.00">[root] ___ROOT___</text>
</g>
<!-- [root] ___ROOT___#build&#45;&gt;[root] ___ROOT___ -->
<g id="edge1" class="edge">
<title>[root] ___ROOT___#build&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M553.55,-77.46C566.44,-75.59 579.69,-73.7 592.19,-72 711.66,-55.72 850.3,-38.8 935.89,-28.58"/>
<polygon fill="black" stroke="black" points="936.55,-32.03 946.06,-27.37 935.72,-25.08 936.55,-32.03"/>
</g>
<!-- [root] akij&#45;food&#45;beverage#build -->
<g id="node3" class="node">
<title>[root] akij&#45;food&#45;beverage#build</title>
<ellipse fill="none" stroke="black" cx="122.19" cy="-306" rx="122.38" ry="18"/>
<text text-anchor="middle" x="122.19" y="-302.3" font-family="Times,serif" font-size="14.00">[root] akij&#45;food&#45;beverage#build</text>
</g>
<!-- [root] server#build -->
<g id="node4" class="node">
<title>[root] server#build</title>
<ellipse fill="none" stroke="black" cx="122.19" cy="-234" rx="77.19" ry="18"/>
<text text-anchor="middle" x="122.19" y="-230.3" font-family="Times,serif" font-size="14.00">[root] server#build</text>
</g>
<!-- [root] akij&#45;food&#45;beverage#build&#45;&gt;[root] server#build -->
<g id="edge2" class="edge">
<title>[root] akij&#45;food&#45;beverage#build&#45;&gt;[root] server#build</title>
<path fill="none" stroke="black" d="M122.19,-287.7C122.19,-279.98 122.19,-270.71 122.19,-262.11"/>
<polygon fill="black" stroke="black" points="125.69,-262.1 122.19,-252.1 118.69,-262.1 125.69,-262.1"/>
</g>
<!-- [root] ui#build -->
<g id="node5" class="node">
<title>[root] ui#build</title>
<ellipse fill="none" stroke="black" cx="304.19" cy="-234" rx="62.29" ry="18"/>
<text text-anchor="middle" x="304.19" y="-230.3" font-family="Times,serif" font-size="14.00">[root] ui#build</text>
</g>
<!-- [root] akij&#45;food&#45;beverage#build&#45;&gt;[root] ui#build -->
<g id="edge3" class="edge">
<title>[root] akij&#45;food&#45;beverage#build&#45;&gt;[root] ui#build</title>
<path fill="none" stroke="black" d="M163.94,-288.94C192.23,-278.06 229.6,-263.69 258.65,-252.51"/>
<polygon fill="black" stroke="black" points="260.08,-255.71 268.16,-248.86 257.57,-249.18 260.08,-255.71"/>
</g>
<!-- [root] server#build&#45;&gt;[root] ___ROOT___#build -->
<g id="edge12" class="edge">
<title>[root] server#build&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M117.88,-215.8C114.02,-195.82 111.35,-163.18 129.19,-144 160.24,-110.6 273.37,-98.13 362.32,-93.54"/>
<polygon fill="black" stroke="black" points="362.67,-97.03 372.49,-93.04 362.33,-90.04 362.67,-97.03"/>
</g>
<!-- [root] eslint&#45;config&#45;custom#build -->
<g id="node8" class="node">
<title>[root] eslint&#45;config&#45;custom#build</title>
<ellipse fill="none" stroke="black" cx="453.19" cy="-162" rx="128.88" ry="18"/>
<text text-anchor="middle" x="453.19" y="-158.3" font-family="Times,serif" font-size="14.00">[root] eslint&#45;config&#45;custom#build</text>
</g>
<!-- [root] ui#build&#45;&gt;[root] eslint&#45;config&#45;custom#build -->
<g id="edge20" class="edge">
<title>[root] ui#build&#45;&gt;[root] eslint&#45;config&#45;custom#build</title>
<path fill="none" stroke="black" d="M335.42,-218.33C356.82,-208.27 385.5,-194.8 409.2,-183.67"/>
<polygon fill="black" stroke="black" points="410.78,-186.79 418.35,-179.37 407.81,-180.45 410.78,-186.79"/>
</g>
<!-- [root] tsconfig#build -->
<g id="node13" class="node">
<title>[root] tsconfig#build</title>
<ellipse fill="none" stroke="black" cx="222.19" cy="-162" rx="83.69" ry="18"/>
<text text-anchor="middle" x="222.19" y="-158.3" font-family="Times,serif" font-size="14.00">[root] tsconfig#build</text>
</g>
<!-- [root] ui#build&#45;&gt;[root] tsconfig#build -->
<g id="edge21" class="edge">
<title>[root] ui#build&#45;&gt;[root] tsconfig#build</title>
<path fill="none" stroke="black" d="M285.17,-216.76C274.56,-207.71 261.16,-196.27 249.45,-186.28"/>
<polygon fill="black" stroke="black" points="251.55,-183.46 241.67,-179.63 247.01,-188.79 251.55,-183.46"/>
</g>
<!-- [root] akij&#45;food&#45;beverage#lint -->
<g id="node6" class="node">
<title>[root] akij&#45;food&#45;beverage#lint</title>
<ellipse fill="none" stroke="black" cx="989.19" cy="-234" rx="116.18" ry="18"/>
<text text-anchor="middle" x="989.19" y="-230.3" font-family="Times,serif" font-size="14.00">[root] akij&#45;food&#45;beverage#lint</text>
</g>
<!-- [root] akij&#45;food&#45;beverage#lint&#45;&gt;[root] ___ROOT___ -->
<g id="edge4" class="edge">
<title>[root] akij&#45;food&#45;beverage#lint&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M1097.67,-227.39C1176.76,-216.56 1276.71,-186.96 1316.19,-108 1323.34,-93.69 1326.47,-84.26 1316.19,-72 1290.06,-40.83 1186.78,-27.98 1108.86,-22.69"/>
<polygon fill="black" stroke="black" points="1108.73,-19.17 1098.52,-22.02 1108.28,-26.16 1108.73,-19.17"/>
</g>
<!-- [root] akij&#45;food&#45;beverage#test -->
<g id="node7" class="node">
<title>[root] akij&#45;food&#45;beverage#test</title>
<ellipse fill="none" stroke="black" cx="379.19" cy="-306" rx="116.18" ry="18"/>
<text text-anchor="middle" x="379.19" y="-302.3" font-family="Times,serif" font-size="14.00">[root] akij&#45;food&#45;beverage#test</text>
</g>
<!-- [root] akij&#45;food&#45;beverage#test&#45;&gt;[root] server#build -->
<g id="edge6" class="edge">
<title>[root] akij&#45;food&#45;beverage#test&#45;&gt;[root] server#build</title>
<path fill="none" stroke="black" d="M324.37,-290.07C281.97,-278.52 223.41,-262.57 180.23,-250.81"/>
<polygon fill="black" stroke="black" points="181,-247.39 170.43,-248.14 179.16,-254.14 181,-247.39"/>
</g>
<!-- [root] akij&#45;food&#45;beverage#test&#45;&gt;[root] ui#build -->
<g id="edge7" class="edge">
<title>[root] akij&#45;food&#45;beverage#test&#45;&gt;[root] ui#build</title>
<path fill="none" stroke="black" d="M361.03,-288.05C351.5,-279.16 339.67,-268.11 329.27,-258.41"/>
<polygon fill="black" stroke="black" points="331.36,-255.57 321.66,-251.31 326.58,-260.69 331.36,-255.57"/>
</g>
<!-- [root] akij&#45;food&#45;beverage#test&#45;&gt;[root] akij&#45;food&#45;beverage#lint -->
<g id="edge5" class="edge">
<title>[root] akij&#45;food&#45;beverage#test&#45;&gt;[root] akij&#45;food&#45;beverage#lint</title>
<path fill="none" stroke="black" d="M469.52,-294.63C581.67,-281.76 772.35,-259.88 888.92,-246.51"/>
<polygon fill="black" stroke="black" points="889.38,-249.98 898.92,-245.36 888.58,-243.02 889.38,-249.98"/>
</g>
<!-- [root] eslint&#45;config&#45;custom#build&#45;&gt;[root] ___ROOT___#build -->
<g id="edge8" class="edge">
<title>[root] eslint&#45;config&#45;custom#build&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M459.12,-143.7C461.8,-135.9 465.01,-126.51 467.99,-117.83"/>
<polygon fill="black" stroke="black" points="471.39,-118.7 471.32,-108.1 464.77,-116.43 471.39,-118.7"/>
</g>
<!-- [root] eslint&#45;config&#45;custom#lint -->
<g id="node9" class="node">
<title>[root] eslint&#45;config&#45;custom#lint</title>
<ellipse fill="none" stroke="black" cx="723.19" cy="-90" rx="122.38" ry="18"/>
<text text-anchor="middle" x="723.19" y="-86.3" font-family="Times,serif" font-size="14.00">[root] eslint&#45;config&#45;custom#lint</text>
</g>
<!-- [root] eslint&#45;config&#45;custom#lint&#45;&gt;[root] ___ROOT___ -->
<g id="edge9" class="edge">
<title>[root] eslint&#45;config&#45;custom#lint&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M784.46,-74.41C834,-62.62 903.51,-46.07 953.65,-34.13"/>
<polygon fill="black" stroke="black" points="954.49,-37.53 963.4,-31.81 952.86,-30.72 954.49,-37.53"/>
</g>
<!-- [root] eslint&#45;config&#45;custom#test -->
<g id="node10" class="node">
<title>[root] eslint&#45;config&#45;custom#test</title>
<ellipse fill="none" stroke="black" cx="723.19" cy="-162" rx="122.68" ry="18"/>
<text text-anchor="middle" x="723.19" y="-158.3" font-family="Times,serif" font-size="14.00">[root] eslint&#45;config&#45;custom#test</text>
</g>
<!-- [root] eslint&#45;config&#45;custom#test&#45;&gt;[root] ___ROOT___#build -->
<g id="edge10" class="edge">
<title>[root] eslint&#45;config&#45;custom#test&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M669.51,-145.72C630.98,-134.76 578.98,-119.96 538.76,-108.52"/>
<polygon fill="black" stroke="black" points="539.65,-105.13 529.07,-105.76 537.73,-111.87 539.65,-105.13"/>
</g>
<!-- [root] eslint&#45;config&#45;custom#test&#45;&gt;[root] eslint&#45;config&#45;custom#lint -->
<g id="edge11" class="edge">
<title>[root] eslint&#45;config&#45;custom#test&#45;&gt;[root] eslint&#45;config&#45;custom#lint</title>
<path fill="none" stroke="black" d="M723.19,-143.7C723.19,-135.98 723.19,-126.71 723.19,-118.11"/>
<polygon fill="black" stroke="black" points="726.69,-118.1 723.19,-108.1 719.69,-118.1 726.69,-118.1"/>
</g>
<!-- [root] server#lint -->
<g id="node11" class="node">
<title>[root] server#lint</title>
<ellipse fill="none" stroke="black" cx="934.19" cy="-90" rx="70.69" ry="18"/>
<text text-anchor="middle" x="934.19" y="-86.3" font-family="Times,serif" font-size="14.00">[root] server#lint</text>
</g>
<!-- [root] server#lint&#45;&gt;[root] ___ROOT___ -->
<g id="edge13" class="edge">
<title>[root] server#lint&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M953.86,-72.41C964.53,-63.41 977.9,-52.14 989.59,-42.27"/>
<polygon fill="black" stroke="black" points="991.98,-44.84 997.37,-35.71 987.47,-39.48 991.98,-44.84"/>
</g>
<!-- [root] server#test -->
<g id="node12" class="node">
<title>[root] server#test</title>
<ellipse fill="none" stroke="black" cx="935.19" cy="-162" rx="71.49" ry="18"/>
<text text-anchor="middle" x="935.19" y="-158.3" font-family="Times,serif" font-size="14.00">[root] server#test</text>
</g>
<!-- [root] server#test&#45;&gt;[root] ___ROOT___#build -->
<g id="edge14" class="edge">
<title>[root] server#test&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M883.66,-149.47C874.21,-147.52 864.42,-145.61 855.19,-144 738.97,-123.72 708.99,-124.63 592.19,-108 582.99,-106.69 573.39,-105.3 563.82,-103.91"/>
<polygon fill="black" stroke="black" points="564.28,-100.44 553.88,-102.45 563.27,-107.36 564.28,-100.44"/>
</g>
<!-- [root] server#test&#45;&gt;[root] server#lint -->
<g id="edge15" class="edge">
<title>[root] server#test&#45;&gt;[root] server#lint</title>
<path fill="none" stroke="black" d="M934.94,-143.7C934.83,-135.98 934.7,-126.71 934.58,-118.11"/>
<polygon fill="black" stroke="black" points="938.08,-118.05 934.43,-108.1 931.08,-118.15 938.08,-118.05"/>
</g>
<!-- [root] tsconfig#build&#45;&gt;[root] ___ROOT___#build -->
<g id="edge16" class="edge">
<title>[root] tsconfig#build&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M271.66,-147.42C312.32,-136.26 370.19,-120.37 414.17,-108.3"/>
<polygon fill="black" stroke="black" points="415.18,-111.65 423.9,-105.63 413.33,-104.9 415.18,-111.65"/>
</g>
<!-- [root] tsconfig#lint -->
<g id="node14" class="node">
<title>[root] tsconfig#lint</title>
<ellipse fill="none" stroke="black" cx="1100.19" cy="-90" rx="77.19" ry="18"/>
<text text-anchor="middle" x="1100.19" y="-86.3" font-family="Times,serif" font-size="14.00">[root] tsconfig#lint</text>
</g>
<!-- [root] tsconfig#lint&#45;&gt;[root] ___ROOT___ -->
<g id="edge17" class="edge">
<title>[root] tsconfig#lint&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M1080.52,-72.41C1069.85,-63.41 1056.48,-52.14 1044.78,-42.27"/>
<polygon fill="black" stroke="black" points="1046.91,-39.48 1037.01,-35.71 1042.39,-44.84 1046.91,-39.48"/>
</g>
<!-- [root] tsconfig#test -->
<g id="node15" class="node">
<title>[root] tsconfig#test</title>
<ellipse fill="none" stroke="black" cx="1102.19" cy="-162" rx="77.99" ry="18"/>
<text text-anchor="middle" x="1102.19" y="-158.3" font-family="Times,serif" font-size="14.00">[root] tsconfig#test</text>
</g>
<!-- [root] tsconfig#test&#45;&gt;[root] ___ROOT___#build -->
<g id="edge18" class="edge">
<title>[root] tsconfig#test&#45;&gt;[root] ___ROOT___#build</title>
<path fill="none" stroke="black" d="M1046.65,-149.22C1036.23,-147.27 1025.4,-145.43 1015.19,-144 828.33,-117.87 779.6,-129.81 592.19,-108 582.49,-106.87 572.35,-105.56 562.29,-104.18"/>
<polygon fill="black" stroke="black" points="562.65,-100.7 552.26,-102.78 561.68,-107.63 562.65,-100.7"/>
</g>
<!-- [root] tsconfig#test&#45;&gt;[root] tsconfig#lint -->
<g id="edge19" class="edge">
<title>[root] tsconfig#test&#45;&gt;[root] tsconfig#lint</title>
<path fill="none" stroke="black" d="M1101.69,-143.7C1101.47,-135.98 1101.21,-126.71 1100.96,-118.11"/>
<polygon fill="black" stroke="black" points="1104.46,-118 1100.68,-108.1 1097.46,-118.2 1104.46,-118"/>
</g>
<!-- [root] ui#lint -->
<g id="node16" class="node">
<title>[root] ui#lint</title>
<ellipse fill="none" stroke="black" cx="1251.19" cy="-90" rx="55.79" ry="18"/>
<text text-anchor="middle" x="1251.19" y="-86.3" font-family="Times,serif" font-size="14.00">[root] ui#lint</text>
</g>
<!-- [root] ui#lint&#45;&gt;[root] ___ROOT___ -->
<g id="edge22" class="edge">
<title>[root] ui#lint&#45;&gt;[root] ___ROOT___</title>
<path fill="none" stroke="black" d="M1211.76,-77.2C1173.94,-65.89 1116.43,-48.69 1073.73,-35.92"/>
<polygon fill="black" stroke="black" points="1074.63,-32.53 1064.04,-33.02 1072.62,-39.24 1074.63,-32.53"/>
</g>
<!-- [root] ui#test -->
<g id="node17" class="node">
<title>[root] ui#test</title>
<ellipse fill="none" stroke="black" cx="453.19" cy="-234" rx="56.59" ry="18"/>
<text text-anchor="middle" x="453.19" y="-230.3" font-family="Times,serif" font-size="14.00">[root] ui#test</text>
</g>
<!-- [root] ui#test&#45;&gt;[root] eslint&#45;config&#45;custom#build -->
<g id="edge23" class="edge">
<title>[root] ui#test&#45;&gt;[root] eslint&#45;config&#45;custom#build</title>
<path fill="none" stroke="black" d="M453.19,-215.7C453.19,-207.98 453.19,-198.71 453.19,-190.11"/>
<polygon fill="black" stroke="black" points="456.69,-190.1 453.19,-180.1 449.69,-190.1 456.69,-190.1"/>
</g>
<!-- [root] ui#test&#45;&gt;[root] tsconfig#build -->
<g id="edge24" class="edge">
<title>[root] ui#test&#45;&gt;[root] tsconfig#build</title>
<path fill="none" stroke="black" d="M413.74,-221.05C376.52,-209.77 320.33,-192.74 278.42,-180.04"/>
<polygon fill="black" stroke="black" points="279.22,-176.63 268.64,-177.07 277.19,-183.32 279.22,-176.63"/>
</g>
<!-- [root] ui#test&#45;&gt;[root] ui#lint -->
<g id="edge25" class="edge">
<title>[root] ui#test&#45;&gt;[root] ui#lint</title>
<path fill="none" stroke="black" d="M508.62,-230.31C674.88,-222.09 1159.7,-196.77 1189.19,-180 1213.64,-166.1 1230.66,-138.12 1240.6,-117.23"/>
<polygon fill="black" stroke="black" points="1243.88,-118.48 1244.79,-107.92 1237.5,-115.6 1243.88,-118.48"/>
</g>
</g>
</svg>
