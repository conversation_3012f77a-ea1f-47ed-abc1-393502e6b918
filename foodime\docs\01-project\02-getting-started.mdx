---
title: Getting Started
date: "Dec 19, 2022"
description: How to setup the local environment for this repository
tags: ["setup"]
---

## Local environment

The only requirement is Node.js v16 up

It's recommended to use a version manager, so the Node version can be easily changed. Then, asdf is just better than nvm because you can also manage other software with it.

## Install

Clone the repository:

    <NAME_EMAIL>:AkijVenture/akij-turborepo.git

Update the environment variables:

    Update your (.env file) with the content from XXX (ToDo).

Install dependencies:

    npm install

## Run

Run the project on your local: port 3010

    npm run dev

The web app is connecting to the Server, make sure it's running on port 4000
