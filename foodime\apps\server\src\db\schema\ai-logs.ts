import { pgTable, uuid, timestamp, text, numeric } from 'drizzle-orm/pg-core';
import { users } from './users';
import { tenants, locations } from './tenants';

// AI call logs
export const aiCallLogs = pgTable('ai_call_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  tokenUsage: numeric('token_usage').notNull(),
  model: text('model').notNull(),
  tenantId: uuid('tenant_id').references(() => tenants.id),
  locationId: uuid('location_id').references(() => locations.id),
  userId: text('user_id').references(() => users.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type AiCallLog = typeof aiCallLogs.$inferSelect;
export type NewAiCallLog = typeof aiCallLogs.$inferInsert;
