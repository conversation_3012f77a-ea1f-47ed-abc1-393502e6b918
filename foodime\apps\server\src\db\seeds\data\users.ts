import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { users, type NewUser } from "../../schema/users";
import type { SeederData } from "../types";

export const generate = async (): Promise<SeederData<NewUser>> => {
  const records: NewUser[] = [];

  // Create a super admin user
  const superAdminPassword = await bcrypt.hash('admin123', 12);
  const superAdmin: NewUser = {
    id: uuidv4(),
    email: '<EMAIL>',
    username: 'super_admin',
    firstName: 'Super',
    lastName: 'Admin',
    isVerified: true,
    isActive: true,
    role: 'SUPER_ADMIN',
    password: superAdminPassword,
  };
  records.push(superAdmin);

  // Create a admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin: NewUser = {
    id: uuidv4(),
    email: '<EMAIL>',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'Admin',
    isVerified: true,
    isActive: true,
    role: 'ADMIN',
    password: adminPassword,
  };

  records.push(admin);

  // Create a restaurant owner
  const ownerPassword = await bcrypt.hash('owner123', 12);
  const restaurantOwner: NewUser = {
    id: uuidv4(),
    email: '<EMAIL>',
    username: 'restaurant_owner2',
    firstName: 'Restaurant',
    lastName: 'Owner',
    isVerified: true,
    isActive: true,
    role: 'RESTAURANT_OWNER',
    password: ownerPassword,
  };
  records.push(restaurantOwner);

  // Create regular users
  for (let i = 0; i < 5; i++) {
    const password = await bcrypt.hash('user123', 12);

    const user: NewUser = {
      id: uuidv4(),
      email: `user${i + 1}@example.com`,
      username: `user${i + 1}`,
      firstName: `User`,
      lastName: `${i + 1}`,
      isVerified: true,
      isActive: true,
      role: 'USER',
      password,
    };

    records.push(user);
  }

  return { 
    tableName: "users", 
    records,
    table: users 
  };
};
