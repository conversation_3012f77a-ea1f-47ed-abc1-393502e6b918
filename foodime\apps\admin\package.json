{"name": "admin-panel", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-types": "tsc --noEmit", "test": "jest", "test:unit": "jest --testPathPattern=unit", "test:integration": "jest --testPathPattern=integration", "test:e2e": "jest --testPathPattern=e2e", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false --passWithNoTests"}, "dependencies": {"@repo/ui": "workspace:*", "database": "workspace:*", "react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.2"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.2", "@eslint/eslintrc": "^3", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.0", "@testing-library/react": "^16.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "@types/jest": "^30.0.0"}}