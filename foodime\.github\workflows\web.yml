name: Web CI/CD

on:
  push:
    branches: [main, dev, 156-github-action]
  pull_request:
    branches: [main, dev]
    paths:
      - "apps/web/**"
      - "packages/**"
      - "pnpm-lock.yaml"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  NODE_VERSION: "18"
  PNPM_VERSION: "9.0.0"

jobs:
  build-and-test:
    name: 🏗️ Build & Test Web
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: 🔍 Debug Info
        run: |
          echo "GitHub Actor: ${{ github.actor }}"
          echo "GitHub Event Name: ${{ github.event_name }}"
          echo "GitHub Ref: ${{ github.ref }}"
          echo "GitHub SHA: ${{ github.sha }}"
          echo "Runner OS: ${{ runner.os }}"

      - name: 🛒 Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📦 Setup pnpm
        uses: pnpm/action-setup@v4
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: 📥 Install dependencies
        run: |
          echo "Current directory: $(pwd)"
          echo "Directory contents:"
          ls -la
          echo "\nInstalling dependencies..."
          pnpm install --frozen-lockfile
          echo "\nVerifying pnpm installation:"
          pnpm -v

      - name: 🔍 Lint
        run: pnpm --filter=web lint

      - name: 🔍 Type check
        run: pnpm --filter=web check-types

      - name: 🏗️ Build
        run: pnpm --filter=web build

      - name: 🧪 Run tests
        run: pnpm --filter=web test:ci
        env:
          NODE_ENV: test
          CI: true
