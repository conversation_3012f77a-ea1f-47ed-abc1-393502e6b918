import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { RegisterFormValues, registerSchema } from "../utils/validation";

export const useRegisterForm = () => {
  const form = useForm<RegisterFormValues>({
    resolver: zod<PERSON>esolver(registerSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      password: "",
      confirmPassword: "",
    },
    mode: "onBlur",
  });

  return form;
};
