{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./*": "./src/*.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.5.0", "@types/node": "^22.15.3", "@types/react": "19.1.0", "@types/react-dom": "19.1.1", "eslint": "^9.27.0", "typescript": "5.8.2"}, "dependencies": {"@tailwindcss/forms": "^0.5.10", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwindcss": "^4.0.15"}}