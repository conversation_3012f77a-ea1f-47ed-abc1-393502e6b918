import { pgTable, text, boolean, timestamp, integer, pgEnum, jsonb } from 'drizzle-orm/pg-core';
import { users } from './users';

// Define the RFLCT code type enum
export const rflctTypeEnum = pgEnum('rflct_type', ['USER_ACCESS', 'FEATURE_UNLOCK', 'PROMOTION', 'SPECIAL_ACTION', 'SYSTEM_COMMAND']);

// RFLCT codes table schema
export const rflctCodes = pgTable('rflct_codes', {
  id: text('id').primaryKey().default('gen_random_uuid()'),
  code: text('code').notNull().unique(), // 4-digit code as per user preference
  type: rflctTypeEnum('type').notNull(),
  description: text('description'),
  isActive: boolean('is_active').notNull().default(true),
  userId: text('user_id').references(() => users.id),
  usageCount: integer('usage_count').notNull().default(0),
  lastUsed: timestamp('last_used'),
  metadata: jsonb('metadata'), // JSON data for additional properties
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the type
export type RFLCTCode = typeof rflctCodes.$inferSelect;
export type NewRFLCTCode = typeof rflctCodes.$inferInsert;
