{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "CommonJS", "moduleResolution": "node", "outDir": "../../dist", "rootDir": "..", "esModuleInterop": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "baseUrl": "..", "paths": {"@/*": ["./*"]}}, "include": ["../**/*"], "exclude": ["../pages", "../app", "node_modules"]}