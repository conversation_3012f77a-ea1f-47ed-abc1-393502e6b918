{"name": "@rflct/ai", "version": "0.0.1", "private": true, "main": "index.ts", "scripts": {"download-models": "ts-node scripts/download-models.ts", "dev": "ts-node-dev --project src/ai-server/tsconfig.json --respawn --transpile-only src/ai-server/index.ts", "build": "tsc -p src/ai-server/tsconfig.json", "start": "node dist/ai-server/index.js", "mobile": "expo start", "mobile:android": "expo start --android", "mobile:ios": "expo start --ios", "mobile:web": "expo start --web", "lint": "next lint", "check-types": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "socket.io": "^4.7.2", "@anthropic-ai/sdk": "^0.59.0", "@google/generative-ai": "^0.21.0", "@huggingface/inference": "^4.7.1", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "@repo/ui": "workspace:*", "axios": "^1.7.9", "elevenlabs": "^0.15.0", "expo": "~53.0.20", "expo-av": "^15.1.5", "expo-constants": "^17.1.7", "expo-font": "^13.3.2", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "form-data": "^4.0.4", "next": "15.3.2", "node-fetch": "^3.3.2", "openai": "^4.67.3", "react": "19.1.1", "react-dom": "19.1.1", "react-native": "0.79.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-web": "^0.19.13", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.14", "ts-node-dev": "^2.0.0", "@babel/core": "^7.25.2", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@tailwindcss/postcss": "^4", "@testing-library/react": "^16.1.0", "@testing-library/react-native": "^13.2.2", "@types/node": "^20", "@types/react": "~19.0.10", "@types/react-dom": "^19", "eslint": "^9.27.0", "eslint-config-next": "15.3.2", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "tailwindcss": "^4", "typescript": "~5.8.3"}}