{"name": "@foodime/mobile", "version": "0.0.0", "private": true, "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "check-types": "tsc --noEmit", "dev": "expo start", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@expo/vector-icons": "^15.0.0", "@hookform/resolvers": "^5.2.1", "@react-navigation/native": "^7.1.17", "@react-navigation/native-stack": "^7.3.25", "@repo/ui": "workspace:*", "expo": "~53.0.20", "expo-constants": "^17.1.7", "expo-font": "^13.3.2", "expo-linking": "^7.1.7", "expo-splash-screen": "^0.30.10", "expo-status-bar": "~2.2.3", "expo-web-browser": "^14.2.0", "i18next": "^25.3.6", "i18next-react-native-language-detector": "^1.0.2", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-hook-form": "^7.62.0", "react-native": "0.79.5", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "zod": "^3.25.76"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.28.0", "@babel/preset-react": "^7.27.1", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.27.0", "@react-native/metro-config": "^0.80.2", "@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "babel-jest": "^30.0.5", "eslint": "^9.27.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "globals": "^16.1.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "react-test-renderer": "^19.1.1", "typescript": "~5.8.3", "typescript-eslint": "^8.32.0"}}