import { pgTable, uuid, timestamp, text } from 'drizzle-orm/pg-core';
import { users } from './users';
import { tenants, locations } from './tenants';

// POS vendors
export const posVendors = pgTable('pos_vendors', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// POS integrations
export const posIntegrations = pgTable('pos_integrations', {
  id: uuid('id').primaryKey().defaultRandom(),
  tenantId: uuid('tenant_id').references(() => tenants.id),
  locationId: uuid('location_id').references(() => locations.id),
  vendorId: uuid('vendor_id').references(() => posVendors.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type PosVendor = typeof posVendors.$inferSelect;
export type NewPosVendor = typeof posVendors.$inferInsert;

export type PosIntegration = typeof posIntegrations.$inferSelect;
export type NewPosIntegration = typeof posIntegrations.$inferInsert;
