# Test Environment Variables
NODE_ENV=test
PORT=3002

# JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only
JWT_EXPIRES_IN=1h

# Test Database (separate from development)
DATABASE_URL=postgresql://postgres:password@localhost:5432/foodime_test_db
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/foodime_test_db

# Redis (optional for tests)
REDIS_URL=redis://localhost:6379/1

# CORS Origins
CORS_ORIGINS=http://localhost:3000,http://localhost:3002

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=1000

# File Upload
UPLOAD_PATH=./test-uploads
MAX_FILE_SIZE=5242880

# Email (test configuration)
EMAIL_HOST=localhost
EMAIL_PORT=1025
EMAIL_USER=<EMAIL>
EMAIL_PASS=testpassword

# Test-specific settings
TEST_PORT=3002
LOG_LEVEL=error
JEST_SMOKE_TEST=''

# Auth0 Configuration
AUTH0_DOMAIN=your-auth0-domain
AUTH0_CLIENT_ID=your-auth0-client-id
AUTH0_CLIENT_SECRET=your-auth0-client-secret