import { pgTable, uuid, timestamp, text } from 'drizzle-orm/pg-core';
import { users } from './users';
import { subscriptions } from './subscriptions';

// Supported languages
export const supportedLanguages = pgTable('supported_languages', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: text('name').notNull(),
  code: text('code').notNull(),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Subscription languages
export const subscriptionLanguages = pgTable('subscription_languages', {
  id: uuid('id').primaryKey().defaultRandom(),
  subscriptionId: uuid('subscription_id').references(() => subscriptions.id),
  languageId: uuid('language_id').references(() => supportedLanguages.id),
  createdBy: text('created_by').references(() => users.id),
  updatedBy: text('updated_by').references(() => users.id),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the types
export type SupportedLanguage = typeof supportedLanguages.$inferSelect;
export type NewSupportedLanguage = typeof supportedLanguages.$inferInsert;

export type SubscriptionLanguage = typeof subscriptionLanguages.$inferSelect;
export type NewSubscriptionLanguage = typeof subscriptionLanguages.$inferInsert;
