import { pgTable, text, boolean, timestamp, pgEnum } from 'drizzle-orm/pg-core';

// Define the user role enum
export const userRoleEnum = pgEnum('user_role', ['USER', 'ADMIN', 'SUPER_ADMIN', 'RESTAURANT_OWNER']);

// Users table schema
export const users = pgTable('users', {
  id: text('id').primaryKey().default('gen_random_uuid()'),
  email: text('email').notNull().unique(),
  username: text('username').unique(),
  firstName: text('first_name'),
  lastName: text('last_name'),
  avatar: text('avatar'),
  phone: text('phone'),
  dateOfBirth: timestamp('date_of_birth'),
  isVerified: boolean('is_verified').notNull().default(false),
  isActive: boolean('is_active').notNull().default(true),
  role: userRoleEnum('role').notNull().default('USER'),
  password: text('password').notNull(),
  lastLogin: timestamp('last_login'),
  createdAt: timestamp('created_at').notNull().defaultNow(),
  updatedAt: timestamp('updated_at').notNull().defaultNow(),
});

// Export the type
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
