{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"target": "ES2022", "module": "CommonJS", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "allowSyntheticDefaultImports": true, "moduleResolution": "node", "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node", "jest"]}, "include": ["src/**/*", "prisma/seed.ts"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"], "ts-node": {"files": true}}