version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    restart: always
    environment:
      POSTGRES_DB: foodime_db
      POSTGRES_USER: foodime_user
      POSTGRES_PASSWORD: foodime_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - foodime-network

  redis:
    image: redis:7-alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - foodime-network

  adminer:
    image: adminer
    restart: always
    ports:
      - "8080:8080"
    networks:
      - foodime-network

volumes:
  postgres_data:
  redis_data:

networks:
  foodime-network:
    driver: bridge
